import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { SSHConnection } from '../types';

interface SSHConnectionScreenProps {
  onConnect: (config: SSHConnection) => Promise<boolean>;
  isConnecting: boolean;
}

export const SSHConnectionScreen: React.FC<SSHConnectionScreenProps> = ({
  onConnect,
  isConnecting,
}) => {
  const [config, setConfig] = useState<SSHConnection>({
    host: '',
    port: 22,
    username: '',
    password: '',
    privateKey: '',
    passphrase: '',
  });

  const [authMethod, setAuthMethod] = useState<'password' | 'key'>('password');

  const handleConnect = async () => {
    if (!config.host || !config.username) {
      Alert.alert('错误', '请填写主机地址和用户名');
      return;
    }

    if (authMethod === 'password' && !config.password) {
      Alert.alert('错误', '请填写密码');
      return;
    }

    if (authMethod === 'key' && !config.privateKey) {
      Alert.alert('错误', '请填写私钥');
      return;
    }

    try {
      const success = await onConnect(config);
      if (!success) {
        Alert.alert('连接失败', '无法连接到SSH服务器，请检查配置');
      }
    } catch (error) {
      Alert.alert('连接错误', error instanceof Error ? error.message : '未知错误');
    }
  };

  const updateConfig = (field: keyof SSHConnection, value: string | number) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <Text style={styles.title}>SSH连接配置</Text>
            <Text style={styles.subtitle}>连接到远程Linux服务器</Text>
          </View>

          <View style={styles.form}>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>主机地址</Text>
              <TextInput
                style={styles.input}
                value={config.host}
                onChangeText={(text) => updateConfig('host', text)}
                placeholder="例如: *************"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>端口</Text>
              <TextInput
                style={styles.input}
                value={config.port.toString()}
                onChangeText={(text) => updateConfig('port', parseInt(text) || 22)}
                placeholder="22"
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>用户名</Text>
              <TextInput
                style={styles.input}
                value={config.username}
                onChangeText={(text) => updateConfig('username', text)}
                placeholder="用户名"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.authMethodContainer}>
              <Text style={styles.label}>认证方式</Text>
              <View style={styles.authMethodButtons}>
                <TouchableOpacity
                  style={[
                    styles.authMethodButton,
                    authMethod === 'password' && styles.authMethodButtonActive,
                  ]}
                  onPress={() => setAuthMethod('password')}
                >
                  <Text
                    style={[
                      styles.authMethodButtonText,
                      authMethod === 'password' && styles.authMethodButtonTextActive,
                    ]}
                  >
                    密码
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.authMethodButton,
                    authMethod === 'key' && styles.authMethodButtonActive,
                  ]}
                  onPress={() => setAuthMethod('key')}
                >
                  <Text
                    style={[
                      styles.authMethodButtonText,
                      authMethod === 'key' && styles.authMethodButtonTextActive,
                    ]}
                  >
                    私钥
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {authMethod === 'password' ? (
              <View style={styles.inputGroup}>
                <Text style={styles.label}>密码</Text>
                <TextInput
                  style={styles.input}
                  value={config.password}
                  onChangeText={(text) => updateConfig('password', text)}
                  placeholder="密码"
                  secureTextEntry
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>
            ) : (
              <>
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>私钥</Text>
                  <TextInput
                    style={[styles.input, styles.multilineInput]}
                    value={config.privateKey}
                    onChangeText={(text) => updateConfig('privateKey', text)}
                    placeholder="-----BEGIN PRIVATE KEY-----"
                    multiline
                    numberOfLines={4}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                </View>
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>私钥密码（可选）</Text>
                  <TextInput
                    style={styles.input}
                    value={config.passphrase}
                    onChangeText={(text) => updateConfig('passphrase', text)}
                    placeholder="私钥密码"
                    secureTextEntry
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                </View>
              </>
            )}
          </View>

          <TouchableOpacity
            style={[styles.connectButton, isConnecting && styles.connectButtonDisabled]}
            onPress={handleConnect}
            disabled={isConnecting}
          >
            <Text style={styles.connectButtonText}>
              {isConnecting ? '连接中...' : '连接'}
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  form: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  authMethodContainer: {
    marginBottom: 20,
  },
  authMethodButtons: {
    flexDirection: 'row',
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  authMethodButton: {
    flex: 1,
    padding: 12,
    backgroundColor: '#f9f9f9',
    alignItems: 'center',
  },
  authMethodButtonActive: {
    backgroundColor: '#007AFF',
  },
  authMethodButtonText: {
    fontSize: 16,
    color: '#333',
  },
  authMethodButtonTextActive: {
    color: 'white',
    fontWeight: '600',
  },
  connectButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginTop: 'auto',
  },
  connectButtonDisabled: {
    backgroundColor: '#ccc',
  },
  connectButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
});
