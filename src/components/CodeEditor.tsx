import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  PanResponder,
  Alert,
} from 'react-native';
import SyntaxHighlighter from 'react-native-syntax-highlighter';
import { docco } from 'react-native-syntax-highlighter/styles/hljs';
import { FileContent, CodeSelection, CodeContext } from '../types';

interface CodeEditorProps {
  fileContent: FileContent;
  onCodeSelect?: (context: CodeContext) => void;
  onClose?: () => void;
}

export const CodeEditor: React.FC<CodeEditorProps> = ({
  fileContent,
  onCodeSelect,
  onClose,
}) => {
  const [selection, setSelection] = useState<CodeSelection | null>(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const lines = fileContent.content.split('\n');
  const { width: screenWidth } = Dimensions.get('window');

  // 创建选择手势处理器
  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    
    onPanResponderGrant: (evt) => {
      setIsSelecting(true);
      const { locationY } = evt.nativeEvent;
      const lineHeight = 20; // 估算行高
      const startLine = Math.floor(locationY / lineHeight);
      
      setSelection({
        startLine: Math.max(0, startLine),
        endLine: Math.max(0, startLine),
        startColumn: 0,
        endColumn: 0,
        selectedText: '',
      });
    },

    onPanResponderMove: (evt) => {
      if (!isSelecting || !selection) return;
      
      const { locationY } = evt.nativeEvent;
      const lineHeight = 20;
      const currentLine = Math.floor(locationY / lineHeight);
      
      setSelection(prev => prev ? {
        ...prev,
        endLine: Math.max(0, Math.min(lines.length - 1, currentLine)),
      } : null);
    },

    onPanResponderRelease: () => {
      setIsSelecting(false);
      if (selection && selection.startLine !== selection.endLine) {
        handleSelectionComplete();
      }
    },
  });

  const handleSelectionComplete = () => {
    if (!selection) return;

    const startLine = Math.min(selection.startLine, selection.endLine);
    const endLine = Math.max(selection.startLine, selection.endLine);
    
    const selectedLines = lines.slice(startLine, endLine + 1);
    const selectedText = selectedLines.join('\n');

    const context: CodeContext = {
      filePath: fileContent.path,
      selectedCode: selectedText,
      startLine: startLine + 1, // 1-based line numbers
      endLine: endLine + 1,
      language: fileContent.language || 'text',
    };

    Alert.alert(
      '代码选择',
      `已选择 ${endLine - startLine + 1} 行代码`,
      [
        { text: '取消', style: 'cancel', onPress: () => setSelection(null) },
        { 
          text: '发送给AI', 
          onPress: () => {
            onCodeSelect?.(context);
            setSelection(null);
          }
        },
      ]
    );
  };

  const renderLineNumbers = () => {
    return (
      <View style={styles.lineNumbers}>
        {lines.map((_, index) => (
          <Text key={index} style={styles.lineNumber}>
            {index + 1}
          </Text>
        ))}
      </View>
    );
  };

  const renderSelectionOverlay = () => {
    if (!selection) return null;

    const startLine = Math.min(selection.startLine, selection.endLine);
    const endLine = Math.max(selection.startLine, selection.endLine);
    const lineHeight = 20;

    return (
      <View
        style={[
          styles.selectionOverlay,
          {
            top: startLine * lineHeight,
            height: (endLine - startLine + 1) * lineHeight,
          },
        ]}
      />
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.fileName}>{fileContent.path.split('/').pop()}</Text>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Text style={styles.closeButtonText}>✕</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.editorContainer}>
        <ScrollView
          ref={scrollViewRef}
          style={styles.scrollView}
          horizontal
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={true}
        >
          <View style={styles.codeContainer} {...panResponder.panHandlers}>
            {renderLineNumbers()}
            <View style={styles.codeContent}>
              {renderSelectionOverlay()}
              <SyntaxHighlighter
                language={fileContent.language || 'text'}
                style={docco}
                customStyle={styles.syntaxHighlighter}
                fontSize={14}
                highlighter="hljs"
              >
                {fileContent.content}
              </SyntaxHighlighter>
            </View>
          </View>
        </ScrollView>
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          {fileContent.language?.toUpperCase()} • {lines.length} 行
        </Text>
        <Text style={styles.footerHint}>
          长按并拖拽选择代码片段
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  fileName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666',
  },
  editorContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  scrollView: {
    flex: 1,
  },
  codeContainer: {
    flexDirection: 'row',
    minWidth: '100%',
  },
  lineNumbers: {
    backgroundColor: '#f8f8f8',
    paddingHorizontal: 8,
    paddingVertical: 16,
    borderRightWidth: 1,
    borderRightColor: '#e0e0e0',
  },
  lineNumber: {
    fontSize: 12,
    color: '#999',
    fontFamily: 'Courier',
    lineHeight: 20,
    textAlign: 'right',
    minWidth: 30,
  },
  codeContent: {
    flex: 1,
    position: 'relative',
  },
  syntaxHighlighter: {
    backgroundColor: 'transparent',
    padding: 16,
    fontFamily: 'Courier',
    fontSize: 14,
    lineHeight: 20,
  },
  selectionOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 122, 255, 0.2)',
    zIndex: 1,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f8f8f8',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  footerText: {
    fontSize: 12,
    color: '#666',
  },
  footerHint: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
});
