import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface ProjectPathModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (path: string) => void;
  currentPath?: string;
}

export const ProjectPathModal: React.FC<ProjectPathModalProps> = ({
  visible,
  onClose,
  onConfirm,
  currentPath = '/home/<USER>/project',
}) => {
  const [projectPath, setProjectPath] = useState(currentPath);

  const handleConfirm = () => {
    if (!projectPath.trim()) {
      Alert.alert('错误', '请输入项目路径');
      return;
    }

    if (!projectPath.startsWith('/')) {
      Alert.alert('错误', '请输入绝对路径（以 / 开头）');
      return;
    }

    onConfirm(projectPath.trim());
    onClose();
  };

  const handleCancel = () => {
    setProjectPath(currentPath);
    onClose();
  };

  const commonPaths = [
    '/home/<USER>/project',
    '/home/<USER>/workspace',
    '/var/www/html',
    '/opt/app',
    '/usr/src/app',
  ];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>设置项目路径</Text>
          <Text style={styles.subtitle}>选择或输入远程服务器上的项目目录</Text>
        </View>

        <View style={styles.content}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>项目路径</Text>
            <TextInput
              style={styles.input}
              value={projectPath}
              onChangeText={setProjectPath}
              placeholder="/home/<USER>/project"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.commonPathsContainer}>
            <Text style={styles.commonPathsTitle}>常用路径</Text>
            {commonPaths.map((path) => (
              <TouchableOpacity
                key={path}
                style={styles.commonPathItem}
                onPress={() => setProjectPath(path)}
              >
                <Text style={styles.commonPathText}>{path}</Text>
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.infoContainer}>
            <Text style={styles.infoTitle}>💡 提示</Text>
            <Text style={styles.infoText}>
              • 确保路径存在于远程服务器上{'\n'}
              • 建议使用包含代码文件的目录{'\n'}
              • 路径必须是绝对路径（以 / 开头）
            </Text>
          </View>
        </View>

        <View style={styles.footer}>
          <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
            <Text style={styles.cancelButtonText}>取消</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.confirmButton} onPress={handleConfirm}>
            <Text style={styles.confirmButtonText}>确认</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  inputGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: 'white',
    fontFamily: 'Courier',
  },
  commonPathsContainer: {
    marginBottom: 24,
  },
  commonPathsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  commonPathItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  commonPathText: {
    fontSize: 14,
    color: '#007AFF',
    fontFamily: 'Courier',
  },
  infoContainer: {
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  footer: {
    flexDirection: 'row',
    padding: 20,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  confirmButton: {
    flex: 1,
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginLeft: 8,
  },
  confirmButtonText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '600',
  },
});
