// SSH连接相关类型
export interface SSHConnection {
  host: string;
  port: number;
  username: string;
  password?: string;
  privateKey?: string;
  passphrase?: string;
}

export interface SSHConnectionStatus {
  isConnected: boolean;
  isConnecting: boolean;
  error?: string;
  lastConnected?: Date;
}

// 文件系统相关类型
export interface RemoteFile {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  modifiedTime?: Date;
  permissions?: string;
}

export interface FileContent {
  path: string;
  content: string;
  language?: string;
  encoding?: string;
}

// AI助手相关类型
export interface AIAgent {
  id: string;
  name: string;
  type: 'claude-code' | 'gemini-cli';
  status: 'idle' | 'running' | 'busy' | 'error';
  version?: string;
}

export interface AIMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  context?: CodeContext;
}

export interface CodeContext {
  filePath: string;
  selectedCode: string;
  startLine: number;
  endLine: number;
  language: string;
}

// 代码编辑相关类型
export interface CodeSelection {
  startLine: number;
  endLine: number;
  startColumn: number;
  endColumn: number;
  selectedText: string;
}

export interface CodeChange {
  filePath: string;
  oldContent: string;
  newContent: string;
  description: string;
  timestamp: Date;
}

// 应用状态相关类型
export interface AppState {
  sshConnection: SSHConnectionStatus;
  currentDirectory: string;
  selectedFiles: RemoteFile[];
  activeAgent?: AIAgent;
  chatHistory: AIMessage[];
  isLoading: boolean;
}

// 配置相关类型
export interface AppConfig {
  defaultSSHPort: number;
  maxChatHistory: number;
  codeHighlightTheme: string;
  autoSaveInterval: number;
}
