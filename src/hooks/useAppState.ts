import { useState, useEffect, useCallback } from 'react';
import { AppState, SSHConnection, RemoteFile, AIMessage, CodeContext } from '../types';
import { sshService } from '../services/SSHService';
import { aiAgentManager } from '../services/AIAgentService';

const initialState: AppState = {
  sshConnection: {
    isConnected: false,
    isConnecting: false,
  },
  currentDirectory: '/',
  selectedFiles: [],
  chatHistory: [],
  isLoading: false,
};

export const useAppState = () => {
  const [state, setState] = useState<AppState>(initialState);

  // SSH连接管理
  const connectSSH = useCallback(async (config: SSHConnection): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true }));
    
    try {
      const success = await sshService.connect(config);
      if (success) {
        // 连接成功后，获取初始目录内容
        const files = await sshService.listDirectory('/');
        setState(prev => ({
          ...prev,
          selectedFiles: files,
          currentDirectory: '/',
        }));
      }
      return success;
    } catch (error) {
      console.error('SSH connection failed:', error);
      return false;
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  const disconnectSSH = useCallback(async (): Promise<void> => {
    await sshService.disconnect();
    await aiAgentManager.stopActiveAgent();
    setState(prev => ({
      ...prev,
      selectedFiles: [],
      currentDirectory: '/',
      activeAgent: undefined,
      chatHistory: [],
    }));
  }, []);

  // 文件系统操作
  const navigateToDirectory = useCallback(async (path: string): Promise<void> => {
    setState(prev => ({ ...prev, isLoading: true }));
    
    try {
      const files = await sshService.listDirectory(path);
      setState(prev => ({
        ...prev,
        selectedFiles: files,
        currentDirectory: path,
      }));
    } catch (error) {
      console.error('Failed to navigate to directory:', error);
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  const refreshCurrentDirectory = useCallback(async (): Promise<void> => {
    await navigateToDirectory(state.currentDirectory);
  }, [state.currentDirectory, navigateToDirectory]);

  // AI代理管理
  const setActiveAgent = useCallback(async (agentId: string): Promise<boolean> => {
    try {
      const success = await aiAgentManager.setActiveAgent(agentId);
      if (success) {
        const agent = aiAgentManager.getActiveAgent();
        setState(prev => ({
          ...prev,
          activeAgent: agent?.getAgent(),
        }));
      }
      return success;
    } catch (error) {
      console.error('Failed to set active agent:', error);
      return false;
    }
  }, []);

  const startAIAgent = useCallback(async (projectPath: string): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true }));
    
    try {
      const success = await aiAgentManager.startActiveAgent(projectPath);
      if (success) {
        const agent = aiAgentManager.getActiveAgent();
        setState(prev => ({
          ...prev,
          activeAgent: agent?.getAgent(),
        }));
      }
      return success;
    } catch (error) {
      console.error('Failed to start AI agent:', error);
      return false;
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  const stopAIAgent = useCallback(async (): Promise<void> => {
    await aiAgentManager.stopActiveAgent();
    const agent = aiAgentManager.getActiveAgent();
    setState(prev => ({
      ...prev,
      activeAgent: agent?.getAgent(),
    }));
  }, []);

  const sendMessageToAI = useCallback(async (message: string, context?: CodeContext): Promise<void> => {
    const activeAgent = aiAgentManager.getActiveAgent();
    if (!activeAgent) {
      throw new Error('No active AI agent');
    }

    await activeAgent.sendMessage(message, context);
  }, []);

  // 聊天历史管理
  const addMessage = useCallback((message: AIMessage): void => {
    setState(prev => ({
      ...prev,
      chatHistory: [...prev.chatHistory, message],
    }));
  }, []);

  const clearChatHistory = useCallback((): void => {
    setState(prev => ({
      ...prev,
      chatHistory: [],
    }));
  }, []);

  // 设置状态监听器
  useEffect(() => {
    // 监听SSH连接状态变化
    const unsubscribeSSH = sshService.onStatusChange((status) => {
      setState(prev => ({
        ...prev,
        sshConnection: status,
      }));
    });

    // 监听AI代理消息
    const activeAgent = aiAgentManager.getActiveAgent();
    let unsubscribeMessages: (() => void) | undefined;
    let unsubscribeAgentStatus: (() => void) | undefined;

    if (activeAgent) {
      unsubscribeMessages = activeAgent.onMessage((message) => {
        addMessage(message);
      });

      unsubscribeAgentStatus = activeAgent.onStatusChange((agent) => {
        setState(prev => ({
          ...prev,
          activeAgent: agent,
        }));
      });
    }

    return () => {
      unsubscribeSSH();
      unsubscribeMessages?.();
      unsubscribeAgentStatus?.();
    };
  }, [addMessage]);

  // 获取可用的AI代理
  const getAvailableAgents = useCallback(() => {
    return aiAgentManager.getAvailableAgents();
  }, []);

  return {
    // 状态
    state,
    
    // SSH操作
    connectSSH,
    disconnectSSH,
    
    // 文件系统操作
    navigateToDirectory,
    refreshCurrentDirectory,
    
    // AI代理操作
    setActiveAgent,
    startAIAgent,
    stopAIAgent,
    sendMessageToAI,
    getAvailableAgents,
    
    // 聊天操作
    addMessage,
    clearChatHistory,
  };
};
