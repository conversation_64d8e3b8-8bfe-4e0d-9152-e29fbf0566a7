import SSHClient from 'react-native-ssh-sftp';
import { File<PERSON>ontent, RemoteFile, SSHConnection, SSHConnectionStatus } from '../types';

export class SSHService {
  private connection: SSHClient | null = null;
  private connectionStatus: SSHConnectionStatus = {
    isConnected: false,
    isConnecting: false,
  };

  private statusListeners: ((status: SSHConnectionStatus) => void)[] = [];

  constructor() {
    // 初始化SSH服务
  }

  /**
   * 连接到SSH服务器
   */
  async connect(config: SSHConnection): Promise<boolean> {
    try {
      this.updateStatus({ isConnecting: true, isConnected: false });

      // 创建SSH连接
      const authConfig = config.privateKey
        ? { privateKey: config.privateKey, passphrase: config.passphrase }
        : config.password;

      this.connection = new SSHClient(
        config.host,
        config.port,
        config.username,
        authConfig,
        (error: any) => {
          if (error) {
            console.error('SSH connection error:', error);
            this.updateStatus({
              isConnected: false,
              isConnecting: false,
              error: error.message || 'Connection failed'
            });
          } else {
            this.updateStatus({
              isConnected: true,
              isConnecting: false,
              lastConnected: new Date()
            });
          }
        }
      );

      // 等待连接建立
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        const checkConnection = () => {
          if (this.connectionStatus.isConnected) {
            clearTimeout(timeout);
            resolve(true);
          } else if (this.connectionStatus.error) {
            clearTimeout(timeout);
            reject(new Error(this.connectionStatus.error));
          } else {
            setTimeout(checkConnection, 100);
          }
        };

        checkConnection();
      });

      return true;
    } catch (error) {
      this.updateStatus({
        isConnected: false,
        isConnecting: false,
        error: error instanceof Error ? error.message : 'Connection failed'
      });
      return false;
    }
  }

  /**
   * 断开SSH连接
   */
  async disconnect(): Promise<void> {
    try {
      if (this.connection) {
        this.connection.disconnect();
        this.connection = null;
      }

      this.updateStatus({ isConnected: false, isConnecting: false });
    } catch (error) {
      console.error('Disconnect error:', error);
    }
  }

  /**
   * 执行SSH命令
   */
  async executeCommand(command: string): Promise<string> {
    if (!this.connectionStatus.isConnected || !this.connection) {
      throw new Error('Not connected to SSH server');
    }

    return new Promise((resolve, reject) => {
      this.connection!.execute(command, (error: any, output: string) => {
        if (error) {
          reject(new Error(`Command execution failed: ${error.message}`));
        } else {
          resolve(output || '');
        }
      });
    });
  }

  /**
   * 列出目录内容
   */
  async listDirectory(path: string): Promise<RemoteFile[]> {
    if (!this.connectionStatus.isConnected || !this.connection) {
      throw new Error('Not connected to SSH server');
    }

    return new Promise((resolve, reject) => {
      // 首先连接SFTP
      this.connection!.connectSFTP((error: any) => {
        if (error) {
          reject(new Error(`Failed to connect SFTP: ${error.message}`));
          return;
        }

        // 列出目录内容
        this.connection!.sftpLs(path, (error: any, response: any[]) => {
          if (error) {
            reject(new Error(`Failed to list directory: ${error.message}`));
            return;
          }

          try {
            const files: RemoteFile[] = response.map((item: any) => ({
              name: item.filename,
              path: `${path}/${item.filename}`.replace(/\/+/g, '/'),
              type: item.isDirectory ? 'directory' : 'file',
              size: item.fileSize,
              modifiedTime: new Date(item.lastModified * 1000),
              permissions: item.permissions,
            }));

            resolve(files);
          } catch (parseError) {
            reject(new Error(`Failed to parse directory listing: ${parseError}`));
          }
        });
      });
    });
  }

  /**
   * 读取文件内容
   */
  async readFile(filePath: string): Promise<FileContent> {
    if (!this.connectionStatus.isConnected || !this.connection) {
      throw new Error('Not connected to SSH server');
    }

    try {
      // 使用cat命令读取文件内容
      const content = await this.executeCommand(`cat "${filePath}"`);

      return {
        path: filePath,
        content,
        language: this.getLanguageFromPath(filePath),
        encoding: 'utf-8',
      };
    } catch (error) {
      throw new Error(`Failed to read file: ${error}`);
    }
  }

  /**
   * 写入文件内容
   */
  async writeFile(filePath: string, content: string): Promise<void> {
    if (!this.connectionStatus.isConnected || !this.connection) {
      throw new Error('Not connected to SSH server');
    }

    try {
      // 使用echo命令写入文件内容（转义特殊字符）
      const escapedContent = content.replace(/'/g, "'\"'\"'");
      await this.executeCommand(`echo '${escapedContent}' > "${filePath}"`);
    } catch (error) {
      throw new Error(`Failed to write file: ${error}`);
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): SSHConnectionStatus {
    return { ...this.connectionStatus };
  }

  /**
   * 订阅连接状态变化
   */
  onStatusChange(listener: (status: SSHConnectionStatus) => void): () => void {
    this.statusListeners.push(listener);
    
    // 返回取消订阅函数
    return () => {
      const index = this.statusListeners.indexOf(listener);
      if (index > -1) {
        this.statusListeners.splice(index, 1);
      }
    };
  }

  private updateStatus(newStatus: Partial<SSHConnectionStatus>): void {
    this.connectionStatus = { ...this.connectionStatus, ...newStatus };
    this.statusListeners.forEach(listener => listener(this.connectionStatus));
  }

  private getLanguageFromPath(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'go': 'go',
      'rs': 'rust',
      'php': 'php',
      'rb': 'ruby',
      'swift': 'swift',
      'kt': 'kotlin',
      'dart': 'dart',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'md': 'markdown',
    };
    
    return languageMap[extension || ''] || 'text';
  }
}

// 单例实例
export const sshService = new SSHService();
