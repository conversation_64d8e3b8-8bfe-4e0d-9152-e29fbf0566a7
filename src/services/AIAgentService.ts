import { <PERSON><PERSON>gent, <PERSON>Message, Code<PERSON>ontext, CodeChange } from '../types';
import { sshService } from './SSHService';

export abstract class BaseAIAgent {
  protected agent: AIAgent;
  protected messageListeners: ((message: AIMessage) => void)[] = [];
  protected statusListeners: ((agent: AIAgent) => void)[] = [];

  constructor(agent: AIAgent) {
    this.agent = agent;
  }

  abstract start(projectPath: string): Promise<boolean>;
  abstract stop(): Promise<void>;
  abstract sendMessage(message: string, context?: CodeContext): Promise<void>;
  abstract isRunning(): boolean;

  getAgent(): AIAgent {
    return { ...this.agent };
  }

  onMessage(listener: (message: AIMessage) => void): () => void {
    this.messageListeners.push(listener);
    return () => {
      const index = this.messageListeners.indexOf(listener);
      if (index > -1) {
        this.messageListeners.splice(index, 1);
      }
    };
  }

  onStatusChange(listener: (agent: <PERSON>A<PERSON>) => void): () => void {
    this.statusListeners.push(listener);
    return () => {
      const index = this.statusListeners.indexOf(listener);
      if (index > -1) {
        this.statusListeners.splice(index, 1);
      }
    };
  }

  protected updateStatus(status: AIAgent['status']): void {
    this.agent.status = status;
    this.statusListeners.forEach(listener => listener(this.agent));
  }

  protected emitMessage(message: AIMessage): void {
    this.messageListeners.forEach(listener => listener(message));
  }

  protected generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export class ClaudeCodeAgent extends BaseAIAgent {
  private process: any = null;
  private currentProjectPath: string = '';

  constructor() {
    super({
      id: 'claude-code-agent',
      name: 'Claude Code',
      type: 'claude-code',
      status: 'idle',
    });
  }

  async start(projectPath: string): Promise<boolean> {
    try {
      this.updateStatus('running');
      this.currentProjectPath = projectPath;

      // 检查SSH连接
      if (!sshService.getConnectionStatus().isConnected) {
        throw new Error('SSH connection required');
      }

      // 启动Claude Code
      const command = `cd ${projectPath} && claude-code`;
      
      // TODO: 实现实际的Claude Code启动逻辑
      // 这里需要通过SSH执行命令并保持进程
      
      // 模拟启动过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      this.emitMessage({
        id: this.generateMessageId(),
        type: 'system',
        content: `Claude Code started in ${projectPath}`,
        timestamp: new Date(),
      });

      return true;
    } catch (error) {
      this.updateStatus('error');
      this.emitMessage({
        id: this.generateMessageId(),
        type: 'system',
        content: `Failed to start Claude Code: ${error}`,
        timestamp: new Date(),
      });
      return false;
    }
  }

  async stop(): Promise<void> {
    try {
      if (this.process) {
        // TODO: 实现实际的进程终止逻辑
        this.process = null;
      }
      
      this.updateStatus('idle');
      this.emitMessage({
        id: this.generateMessageId(),
        type: 'system',
        content: 'Claude Code stopped',
        timestamp: new Date(),
      });
    } catch (error) {
      console.error('Failed to stop Claude Code:', error);
    }
  }

  async sendMessage(message: string, context?: CodeContext): Promise<void> {
    if (!this.isRunning()) {
      throw new Error('Claude Code is not running');
    }

    try {
      this.updateStatus('busy');

      // 构建包含上下文的消息
      let fullMessage = message;
      if (context) {
        fullMessage = `Context: ${context.filePath} (lines ${context.startLine}-${context.endLine})\n\`\`\`${context.language}\n${context.selectedCode}\n\`\`\`\n\nRequest: ${message}`;
      }

      // 发送用户消息
      this.emitMessage({
        id: this.generateMessageId(),
        type: 'user',
        content: message,
        timestamp: new Date(),
        context,
      });

      // TODO: 实现实际的消息发送逻辑
      // 这里需要通过SSH向Claude Code进程发送消息
      
      // 模拟AI响应
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const response = `I understand you want to ${message}. Here's my suggestion for the code in ${context?.filePath || 'your file'}...`;
      
      this.emitMessage({
        id: this.generateMessageId(),
        type: 'assistant',
        content: response,
        timestamp: new Date(),
      });

      this.updateStatus('running');
    } catch (error) {
      this.updateStatus('error');
      this.emitMessage({
        id: this.generateMessageId(),
        type: 'system',
        content: `Error sending message: ${error}`,
        timestamp: new Date(),
      });
    }
  }

  isRunning(): boolean {
    return this.agent.status === 'running' || this.agent.status === 'busy';
  }

  async applyCodeChange(change: CodeChange): Promise<boolean> {
    try {
      await sshService.writeFile(change.filePath, change.newContent);
      
      this.emitMessage({
        id: this.generateMessageId(),
        type: 'system',
        content: `Applied changes to ${change.filePath}: ${change.description}`,
        timestamp: new Date(),
      });
      
      return true;
    } catch (error) {
      this.emitMessage({
        id: this.generateMessageId(),
        type: 'system',
        content: `Failed to apply changes: ${error}`,
        timestamp: new Date(),
      });
      return false;
    }
  }
}

export class GeminiCLIAgent extends BaseAIAgent {
  constructor() {
    super({
      id: 'gemini-cli-agent',
      name: 'Gemini CLI',
      type: 'gemini-cli',
      status: 'idle',
    });
  }

  async start(projectPath: string): Promise<boolean> {
    // TODO: 实现Gemini CLI的启动逻辑
    throw new Error('Gemini CLI agent not implemented yet');
  }

  async stop(): Promise<void> {
    // TODO: 实现Gemini CLI的停止逻辑
  }

  async sendMessage(message: string, context?: CodeContext): Promise<void> {
    // TODO: 实现Gemini CLI的消息发送逻辑
    throw new Error('Gemini CLI agent not implemented yet');
  }

  isRunning(): boolean {
    return false;
  }
}

export class AIAgentManager {
  private agents: Map<string, BaseAIAgent> = new Map();
  private activeAgent: BaseAIAgent | null = null;

  constructor() {
    // 注册可用的AI代理
    this.registerAgent(new ClaudeCodeAgent());
    this.registerAgent(new GeminiCLIAgent());
  }

  registerAgent(agent: BaseAIAgent): void {
    this.agents.set(agent.getAgent().id, agent);
  }

  getAvailableAgents(): AIAgent[] {
    return Array.from(this.agents.values()).map(agent => agent.getAgent());
  }

  async setActiveAgent(agentId: string): Promise<boolean> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      throw new Error(`Agent not found: ${agentId}`);
    }

    // 停止当前活跃的代理
    if (this.activeAgent && this.activeAgent.isRunning()) {
      await this.activeAgent.stop();
    }

    this.activeAgent = agent;
    return true;
  }

  getActiveAgent(): BaseAIAgent | null {
    return this.activeAgent;
  }

  async startActiveAgent(projectPath: string): Promise<boolean> {
    if (!this.activeAgent) {
      throw new Error('No active agent selected');
    }

    return await this.activeAgent.start(projectPath);
  }

  async stopActiveAgent(): Promise<void> {
    if (this.activeAgent) {
      await this.activeAgent.stop();
    }
  }
}

// 单例实例
export const aiAgentManager = new AIAgentManager();
