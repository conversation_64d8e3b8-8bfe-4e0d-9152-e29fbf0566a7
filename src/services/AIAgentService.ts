import { <PERSON>Agent, AI<PERSON>essage, CodeChange, CodeContext } from '../types';
import { sshService } from './SSHService';

export abstract class BaseAIAgent {
  protected agent: AIAgent;
  protected messageListeners: ((message: AIMessage) => void)[] = [];
  protected statusListeners: ((agent: AIAgent) => void)[] = [];

  constructor(agent: AIAgent) {
    this.agent = agent;
  }

  abstract start(projectPath: string): Promise<boolean>;
  abstract stop(): Promise<void>;
  abstract sendMessage(message: string, context?: CodeContext): Promise<void>;
  abstract isRunning(): boolean;

  getAgent(): AIAgent {
    return { ...this.agent };
  }

  onMessage(listener: (message: AIMessage) => void): () => void {
    this.messageListeners.push(listener);
    return () => {
      const index = this.messageListeners.indexOf(listener);
      if (index > -1) {
        this.messageListeners.splice(index, 1);
      }
    };
  }

  onStatusChange(listener: (agent: <PERSON>Agent) => void): () => void {
    this.statusListeners.push(listener);
    return () => {
      const index = this.statusListeners.indexOf(listener);
      if (index > -1) {
        this.statusListeners.splice(index, 1);
      }
    };
  }

  protected updateStatus(status: AIAgent['status']): void {
    this.agent.status = status;
    this.statusListeners.forEach(listener => listener(this.agent));
  }

  protected emitMessage(message: AIMessage): void {
    this.messageListeners.forEach(listener => listener(message));
  }

  protected generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}

export class ClaudeCodeAgent extends BaseAIAgent {
  private process: any = null;

  constructor() {
    super({
      id: 'claude-code-agent',
      name: 'Claude Code',
      type: 'claude-code',
      status: 'idle',
    });
  }

  async start(projectPath: string): Promise<boolean> {
    try {
      this.updateStatus('running');

      // 检查SSH连接
      if (!sshService.getConnectionStatus().isConnected) {
        throw new Error('SSH connection required');
      }

      // 检查Claude Code是否已安装
      try {
        await sshService.executeCommand('which claude-code');
      } catch {
        // 如果Claude Code未安装，提供安装指导
        this.emitMessage({
          id: this.generateMessageId(),
          type: 'system',
          content: `Claude Code not found. Please install it first:\n\nnpm install -g @anthropic-ai/claude-code\n\nOr follow the installation guide at: https://docs.anthropic.com/claude/docs/claude-code`,
          timestamp: new Date(),
        });
        this.updateStatus('error');
        return false;
      }

      // 检查项目目录是否存在
      try {
        await sshService.executeCommand(`test -d "${projectPath}"`);
      } catch {
        throw new Error(`Project directory does not exist: ${projectPath}`);
      }

      // 启动Claude Code
      // 注意：这里需要实际的Claude Code启动逻辑
      // 目前使用模拟实现
      this.emitMessage({
        id: this.generateMessageId(),
        type: 'system',
        content: `Starting Claude Code in ${projectPath}...`,
        timestamp: new Date(),
      });

      // 模拟启动过程
      await new Promise(resolve => setTimeout(resolve, 3000));

      this.emitMessage({
        id: this.generateMessageId(),
        type: 'system',
        content: `Claude Code is now running in ${projectPath}. You can start asking questions about your code!`,
        timestamp: new Date(),
      });

      return true;
    } catch (error) {
      this.updateStatus('error');
      this.emitMessage({
        id: this.generateMessageId(),
        type: 'system',
        content: `Failed to start Claude Code: ${error}`,
        timestamp: new Date(),
      });
      return false;
    }
  }

  async stop(): Promise<void> {
    try {
      if (this.process) {
        // TODO: 实现实际的进程终止逻辑
        this.process = null;
      }
      
      this.updateStatus('idle');
      this.emitMessage({
        id: this.generateMessageId(),
        type: 'system',
        content: 'Claude Code stopped',
        timestamp: new Date(),
      });
    } catch (error) {
      console.error('Failed to stop Claude Code:', error);
    }
  }

  async sendMessage(message: string, context?: CodeContext): Promise<void> {
    if (!this.isRunning()) {
      throw new Error('Claude Code is not running');
    }

    try {
      this.updateStatus('busy');

      // 构建包含上下文的消息
      const fullMessage = context
        ? `Context: ${context.filePath} (lines ${context.startLine}-${context.endLine})\n\`\`\`${context.language}\n${context.selectedCode}\n\`\`\`\n\nRequest: ${message}`
        : message;

      // 发送用户消息
      this.emitMessage({
        id: this.generateMessageId(),
        type: 'user',
        content: message,
        timestamp: new Date(),
        context,
      });

      // TODO: 实现实际的消息发送逻辑
      // 这里需要通过SSH向Claude Code进程发送消息
      // 暂时使用fullMessage变量避免警告
      console.log('Sending message to Claude Code:', fullMessage);

      // 模拟AI响应处理
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 生成更智能的响应
      let response = '';
      if (context) {
        if (message.toLowerCase().includes('分析') || message.toLowerCase().includes('analyze')) {
          response = `我来分析一下 ${context.filePath} 中的这段 ${context.language} 代码：\n\n这段代码的主要功能是...\n\n建议优化：\n1. 可以考虑添加错误处理\n2. 变量命名可以更加清晰\n3. 可以添加注释说明`;
        } else if (message.toLowerCase().includes('优化') || message.toLowerCase().includes('optimize')) {
          response = `针对 ${context.filePath} 中的代码，我建议以下优化：\n\n\`\`\`${context.language}\n// 优化后的代码\n${context.selectedCode.replace(/console\.log/g, '// console.log')}\n\`\`\`\n\n主要改进：\n- 移除了调试代码\n- 优化了性能\n- 提高了可读性`;
        } else if (message.toLowerCase().includes('解释') || message.toLowerCase().includes('explain')) {
          response = `让我解释一下 ${context.filePath} 中这段代码的工作原理：\n\n这段代码主要做了以下几件事：\n1. ...\n2. ...\n3. ...\n\n每一行的作用：\n- 第${context.startLine}行：...\n- 第${context.startLine + 1}行：...`;
        } else {
          response = `关于 ${context.filePath} 中的代码，${message}\n\n基于你提供的代码片段，我的建议是...\n\n如果你需要更具体的帮助，请告诉我你想要实现什么功能。`;
        }
      } else {
        response = `我理解你的问题：${message}\n\n作为你的AI编码助手，我可以帮你：\n- 分析和解释代码\n- 提供优化建议\n- 修复bug\n- 编写新功能\n\n请选择一些代码片段，这样我就能提供更精确的帮助了。`;
      }

      this.emitMessage({
        id: this.generateMessageId(),
        type: 'assistant',
        content: response,
        timestamp: new Date(),
      });

      this.updateStatus('running');
    } catch (error) {
      this.updateStatus('error');
      this.emitMessage({
        id: this.generateMessageId(),
        type: 'system',
        content: `Error sending message: ${error}`,
        timestamp: new Date(),
      });
    }
  }

  isRunning(): boolean {
    return this.agent.status === 'running' || this.agent.status === 'busy';
  }

  async applyCodeChange(change: CodeChange): Promise<boolean> {
    try {
      await sshService.writeFile(change.filePath, change.newContent);
      
      this.emitMessage({
        id: this.generateMessageId(),
        type: 'system',
        content: `Applied changes to ${change.filePath}: ${change.description}`,
        timestamp: new Date(),
      });
      
      return true;
    } catch (error) {
      this.emitMessage({
        id: this.generateMessageId(),
        type: 'system',
        content: `Failed to apply changes: ${error}`,
        timestamp: new Date(),
      });
      return false;
    }
  }
}

export class GeminiCLIAgent extends BaseAIAgent {
  constructor() {
    super({
      id: 'gemini-cli-agent',
      name: 'Gemini CLI',
      type: 'gemini-cli',
      status: 'idle',
    });
  }

  async start(_projectPath: string): Promise<boolean> {
    // TODO: 实现Gemini CLI的启动逻辑
    throw new Error('Gemini CLI agent not implemented yet');
  }

  async stop(): Promise<void> {
    // TODO: 实现Gemini CLI的停止逻辑
  }

  async sendMessage(_message: string, _context?: CodeContext): Promise<void> {
    // TODO: 实现Gemini CLI的消息发送逻辑
    throw new Error('Gemini CLI agent not implemented yet');
  }

  isRunning(): boolean {
    return false;
  }
}

export class AIAgentManager {
  private agents: Map<string, BaseAIAgent> = new Map();
  private activeAgent: BaseAIAgent | null = null;

  constructor() {
    // 注册可用的AI代理
    this.registerAgent(new ClaudeCodeAgent());
    this.registerAgent(new GeminiCLIAgent());
  }

  registerAgent(agent: BaseAIAgent): void {
    this.agents.set(agent.getAgent().id, agent);
  }

  getAvailableAgents(): AIAgent[] {
    return Array.from(this.agents.values()).map(agent => agent.getAgent());
  }

  async setActiveAgent(agentId: string): Promise<boolean> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      throw new Error(`Agent not found: ${agentId}`);
    }

    // 停止当前活跃的代理
    if (this.activeAgent && this.activeAgent.isRunning()) {
      await this.activeAgent.stop();
    }

    this.activeAgent = agent;
    return true;
  }

  getActiveAgent(): BaseAIAgent | null {
    return this.activeAgent;
  }

  async startActiveAgent(projectPath: string): Promise<boolean> {
    if (!this.activeAgent) {
      throw new Error('No active agent selected');
    }

    return await this.activeAgent.start(projectPath);
  }

  async stopActiveAgent(): Promise<void> {
    if (this.activeAgent) {
      await this.activeAgent.stop();
    }
  }
}

// 单例实例
export const aiAgentManager = new AIAgentManager();
