import React, { useState } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>List,
    Modal,
    RefreshControl,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { CodeEditor } from '../../src/components/CodeEditor';
import { useAppState } from '../../src/hooks/useAppState';
import { CodeContext, FileContent, RemoteFile } from '../../src/types';

const FileIcon: React.FC<{ file: RemoteFile }> = ({ file }) => {
  const iconStyle = file.type === 'directory' ? styles.folderIcon : styles.fileIcon;
  const iconText = file.type === 'directory' ? '📁' : '📄';
  
  return <Text style={iconStyle}>{iconText}</Text>;
};

const FileItem: React.FC<{
  file: RemoteFile;
  onPress: (file: RemoteFile) => void;
}> = ({ file, onPress }) => {
  const formatSize = (size?: number) => {
    if (!size) return '';
    if (size < 1024) return `${size}B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`;
    return `${(size / (1024 * 1024)).toFixed(1)}MB`;
  };

  const formatDate = (date?: Date) => {
    if (!date) return '';
    return date.toLocaleDateString();
  };

  return (
    <TouchableOpacity style={styles.fileItem} onPress={() => onPress(file)}>
      <View style={styles.fileItemLeft}>
        <FileIcon file={file} />
        <View style={styles.fileInfo}>
          <Text style={styles.fileName}>{file.name}</Text>
          <Text style={styles.fileDetails}>
            {file.type === 'file' && formatSize(file.size)} {formatDate(file.modifiedTime)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default function FilesScreen() {
  const { state, navigateToDirectory, refreshCurrentDirectory, sendMessageToAI } = useAppState();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFile, setSelectedFile] = useState<FileContent | null>(null);
  const [showEditor, setShowEditor] = useState(false);

  const handleFilePress = async (file: RemoteFile) => {
    if (file.type === 'directory') {
      try {
        await navigateToDirectory(file.path);
      } catch (error) {
        Alert.alert('错误', '无法打开目录');
      }
    } else {
      Alert.alert('文件操作', `选择了文件: ${file.name}`, [
        { text: '取消', style: 'cancel' },
        { text: '查看', onPress: () => handleViewFile(file) },
      ]);
    }
  };

  const handleViewFile = async (file: RemoteFile) => {
    try {
      // 这里需要导入sshService
      // const fileContent = await sshService.readFile(file.path);
      // setSelectedFile(fileContent);
      // setShowEditor(true);

      // 临时模拟文件内容
      const mockContent: FileContent = {
        path: file.path,
        content: `// 模拟文件内容 - ${file.name}\nconsole.log('Hello World');\n\nfunction example() {\n  return 'This is a mock file content';\n}`,
        language: getLanguageFromPath(file.path),
        encoding: 'utf-8',
      };
      setSelectedFile(mockContent);
      setShowEditor(true);
    } catch (error) {
      Alert.alert('错误', '无法读取文件内容');
    }
  };

  const getLanguageFromPath = (filePath: string): string => {
    const extension = filePath.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'go': 'go',
      'rs': 'rust',
      'php': 'php',
      'rb': 'ruby',
      'swift': 'swift',
      'kt': 'kotlin',
      'dart': 'dart',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'md': 'markdown',
    };

    return languageMap[extension || ''] || 'text';
  };

  const handleCodeSelect = async (context: CodeContext) => {
    try {
      await sendMessageToAI(`请帮我分析这段代码：`, context);
      Alert.alert('成功', '代码已发送给AI助手');
    } catch (error) {
      Alert.alert('错误', '发送代码失败');
    }
  };

  const handleCloseEditor = () => {
    setShowEditor(false);
    setSelectedFile(null);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshCurrentDirectory();
    } catch (error) {
      Alert.alert('错误', '刷新失败');
    } finally {
      setRefreshing(false);
    }
  };

  const navigateUp = async () => {
    const parentPath = state.currentDirectory.split('/').slice(0, -1).join('/') || '/';
    if (parentPath !== state.currentDirectory) {
      await navigateToDirectory(parentPath);
    }
  };

  if (!state.sshConnection.isConnected) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.notConnectedContainer}>
          <Text style={styles.notConnectedText}>请先连接SSH服务器</Text>
          <Text style={styles.notConnectedSubtext}>在"连接"标签页中配置并连接到远程服务器</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.pathContainer}>
          <TouchableOpacity style={styles.upButton} onPress={navigateUp}>
            <Text style={styles.upButtonText}>↑</Text>
          </TouchableOpacity>
          <Text style={styles.currentPath}>{state.currentDirectory}</Text>
        </View>
      </View>

      <FlatList
        data={state.selectedFiles}
        keyExtractor={(item) => item.path}
        renderItem={({ item }) => (
          <FileItem file={item} onPress={handleFilePress} />
        )}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        contentContainerStyle={styles.fileList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>目录为空</Text>
          </View>
        }
      />

      {state.isLoading && (
        <View style={styles.loadingOverlay}>
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      )}

      <Modal
        visible={showEditor}
        animationType="slide"
        presentationStyle="fullScreen"
      >
        {selectedFile && (
          <CodeEditor
            fileContent={selectedFile}
            onCodeSelect={handleCodeSelect}
            onClose={handleCloseEditor}
          />
        )}
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  notConnectedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  notConnectedText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
    marginBottom: 8,
  },
  notConnectedSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
  header: {
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    padding: 16,
  },
  pathContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  upButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  upButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  currentPath: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  fileList: {
    padding: 16,
  },
  fileItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  fileItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  folderIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  fileIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  fileDetails: {
    fontSize: 12,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 60,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});
