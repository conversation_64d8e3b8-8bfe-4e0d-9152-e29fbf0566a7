import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useAppState } from '../../src/hooks/useAppState';
import { SSHConnectionScreen } from '../../src/screens/SSHConnectionScreen';

export default function HomeScreen() {
  const { state, connectSSH } = useAppState();

  return (
    <View style={styles.container}>
      <SSHConnectionScreen
        onConnect={connectSSH}
        isConnecting={state.sshConnection.isConnecting}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
