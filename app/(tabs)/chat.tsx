import React, { useEffect, useRef, useState } from 'react';
import {
    <PERSON><PERSON>,
    FlatList,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppState } from '../../src/hooks/useAppState';
import { AIMessage } from '../../src/types';

const MessageBubble: React.FC<{ message: AIMessage }> = ({ message }) => {
  const isUser = message.type === 'user';
  const isSystem = message.type === 'system';

  const bubbleStyle = [
    styles.messageBubble,
    isUser ? styles.userBubble : isSystem ? styles.systemBubble : styles.assistantBubble,
  ];

  const textStyle = [
    styles.messageText,
    isUser ? styles.userText : isSystem ? styles.systemText : styles.assistantText,
  ];

  return (
    <View style={[styles.messageContainer, isUser && styles.userMessageContainer]}>
      <View style={bubbleStyle}>
        {message.context && (
          <View style={styles.contextContainer}>
            <Text style={styles.contextTitle}>
              📄 {message.context.filePath} (行 {message.context.startLine}-{message.context.endLine})
            </Text>
            <View style={styles.codePreview}>
              <Text style={styles.codeText} numberOfLines={3}>
                {message.context.selectedCode}
              </Text>
            </View>
          </View>
        )}
        <Text style={textStyle}>{message.content}</Text>
        <Text style={styles.timestamp}>
          {message.timestamp.toLocaleTimeString()}
        </Text>
      </View>
    </View>
  );
};

const AgentSelector: React.FC<{
  availableAgents: any[];
  activeAgent?: any;
  onSelectAgent: (agentId: string) => void;
  onStartAgent: () => void;
  onStopAgent: () => void;
}> = ({ availableAgents, activeAgent, onSelectAgent, onStartAgent, onStopAgent }) => {
  return (
    <View style={styles.agentSelector}>
      <Text style={styles.agentSelectorTitle}>AI助手</Text>
      <View style={styles.agentButtons}>
        {availableAgents.map((agent) => (
          <TouchableOpacity
            key={agent.id}
            style={[
              styles.agentButton,
              activeAgent?.id === agent.id && styles.agentButtonActive,
            ]}
            onPress={() => onSelectAgent(agent.id)}
          >
            <Text
              style={[
                styles.agentButtonText,
                activeAgent?.id === agent.id && styles.agentButtonTextActive,
              ]}
            >
              {agent.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      
      {activeAgent && (
        <View style={styles.agentControls}>
          <Text style={styles.agentStatus}>
            状态: {activeAgent.status === 'idle' ? '空闲' : 
                   activeAgent.status === 'running' ? '运行中' : 
                   activeAgent.status === 'busy' ? '忙碌' : '错误'}
          </Text>
          {activeAgent.status === 'idle' ? (
            <TouchableOpacity style={styles.startButton} onPress={onStartAgent}>
              <Text style={styles.startButtonText}>启动</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity style={styles.stopButton} onPress={onStopAgent}>
              <Text style={styles.stopButtonText}>停止</Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
};

export default function ChatScreen() {
  const {
    state,
    setActiveAgent,
    startAIAgent,
    stopAIAgent,
    sendMessageToAI,
    getAvailableAgents,
    clearChatHistory,
  } = useAppState();
  
  const [inputText, setInputText] = useState('');
  const [projectPath, setProjectPath] = useState('/home/<USER>/project');
  const flatListRef = useRef<FlatList>(null);

  const availableAgents = getAvailableAgents();

  useEffect(() => {
    // 滚动到最新消息
    if (state.chatHistory.length > 0) {
      flatListRef.current?.scrollToEnd({ animated: true });
    }
  }, [state.chatHistory]);

  const handleSelectAgent = async (agentId: string) => {
    try {
      await setActiveAgent(agentId);
    } catch (error) {
      Alert.alert('错误', '无法选择AI助手');
    }
  };

  const handleStartAgent = async () => {
    if (!state.sshConnection.isConnected) {
      Alert.alert('错误', '请先连接SSH服务器');
      return;
    }

    try {
      const success = await startAIAgent(projectPath);
      if (!success) {
        Alert.alert('错误', '无法启动AI助手');
      }
    } catch (error) {
      Alert.alert('错误', '启动AI助手失败');
    }
  };

  const handleStopAgent = async () => {
    try {
      await stopAIAgent();
    } catch (error) {
      Alert.alert('错误', '停止AI助手失败');
    }
  };

  const handleSendMessage = async () => {
    if (!inputText.trim()) return;
    
    if (!state.activeAgent || state.activeAgent.status !== 'running') {
      Alert.alert('错误', 'AI助手未运行');
      return;
    }

    try {
      await sendMessageToAI(inputText.trim());
      setInputText('');
    } catch (error) {
      Alert.alert('错误', '发送消息失败');
    }
  };

  const handleClearHistory = () => {
    Alert.alert(
      '清除历史',
      '确定要清除所有聊天记录吗？',
      [
        { text: '取消', style: 'cancel' },
        { text: '确定', onPress: clearChatHistory },
      ]
    );
  };

  if (!state.sshConnection.isConnected) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.notConnectedContainer}>
          <Text style={styles.notConnectedText}>请先连接SSH服务器</Text>
          <Text style={styles.notConnectedSubtext}>在"连接"标签页中配置并连接到远程服务器</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <AgentSelector
          availableAgents={availableAgents}
          activeAgent={state.activeAgent}
          onSelectAgent={handleSelectAgent}
          onStartAgent={handleStartAgent}
          onStopAgent={handleStopAgent}
        />

        <View style={styles.chatContainer}>
          <View style={styles.chatHeader}>
            <Text style={styles.chatTitle}>对话历史</Text>
            <TouchableOpacity onPress={handleClearHistory}>
              <Text style={styles.clearButton}>清除</Text>
            </TouchableOpacity>
          </View>

          <FlatList
            ref={flatListRef}
            data={state.chatHistory}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => <MessageBubble message={item} />}
            contentContainerStyle={styles.messagesList}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>开始与AI助手对话</Text>
              </View>
            }
          />

          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textInput}
              value={inputText}
              onChangeText={setInputText}
              placeholder="输入消息..."
              multiline
              maxLength={1000}
            />
            <TouchableOpacity
              style={[
                styles.sendButton,
                (!inputText.trim() || state.activeAgent?.status !== 'running') && styles.sendButtonDisabled,
              ]}
              onPress={handleSendMessage}
              disabled={!inputText.trim() || state.activeAgent?.status !== 'running'}
            >
              <Text style={styles.sendButtonText}>发送</Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  notConnectedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  notConnectedText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
    marginBottom: 8,
  },
  notConnectedSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
  agentSelector: {
    backgroundColor: 'white',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  agentSelectorTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  agentButtons: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  agentButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  agentButtonActive: {
    backgroundColor: '#007AFF',
  },
  agentButtonText: {
    fontSize: 14,
    color: '#333',
  },
  agentButtonTextActive: {
    color: 'white',
  },
  agentControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  agentStatus: {
    fontSize: 14,
    color: '#666',
  },
  startButton: {
    backgroundColor: '#34C759',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },
  startButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  stopButton: {
    backgroundColor: '#FF3B30',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },
  stopButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  chatContainer: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  chatTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  clearButton: {
    color: '#007AFF',
    fontSize: 14,
  },
  messagesList: {
    padding: 16,
  },
  messageContainer: {
    marginBottom: 12,
  },
  userMessageContainer: {
    alignItems: 'flex-end',
  },
  messageBubble: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
  },
  userBubble: {
    backgroundColor: '#007AFF',
  },
  assistantBubble: {
    backgroundColor: 'white',
  },
  systemBubble: {
    backgroundColor: '#f0f0f0',
  },
  messageText: {
    fontSize: 16,
    marginBottom: 4,
  },
  userText: {
    color: 'white',
  },
  assistantText: {
    color: '#333',
  },
  systemText: {
    color: '#666',
  },
  timestamp: {
    fontSize: 12,
    color: '#999',
  },
  contextContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 8,
    padding: 8,
    marginBottom: 8,
  },
  contextTitle: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
    marginBottom: 4,
  },
  codePreview: {
    backgroundColor: '#f8f8f8',
    borderRadius: 4,
    padding: 6,
  },
  codeText: {
    fontSize: 11,
    fontFamily: 'Courier',
    color: '#333',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 60,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    maxHeight: 100,
  },
  sendButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    justifyContent: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#ccc',
  },
  sendButtonText: {
    color: 'white',
    fontWeight: '500',
  },
});
